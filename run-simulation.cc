/*
 * Simple runner for FIFO Queue Simulation
 * Just run the simulation directly
 */

#include "fifo-queue-disc.h"
#include "ns3/core-module.h"

using namespace ns3;

int main()
{
    // Tạo FIFO queue instance
    Ptr<FifoQueueDisc> queue = CreateObject<FifoQueueDisc>();
    
    // Chạy simulation với default settings (đã config sẵn theo yêu cầu thầy)
    queue->RunSimulation();
    
    return 0;
}
