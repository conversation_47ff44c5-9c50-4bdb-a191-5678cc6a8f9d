/*
 * Copyright (c) 2017 Universita' degli Studi di Napoli Federico II
 *
 * SPDX-License-Identifier: GPL-2.0-only
 *
 * Authors: <AUTHORS>
 */

#ifndef FIFO_QUEUE_DISC_H
#define FIFO_QUEUE_DISC_H

#include "queue-disc.h"
#include "ns3/traced-callback.h"
#include "ns3/simulator.h"
#include "ns3/nstime.h"

namespace ns3
{

/**
 * \ingroup traffic-control
 *
 * Enhanced FIFO queue disc with simulation and statistics capabilities.
 * Implements comprehensive network simulation with:
 * - Complex network topology support
 * - Packet loss and delay measurement
 * - Throughput analysis
 * - Configurable bandwidth and traffic parameters
 */
class FifoQueueDisc : public QueueDisc
{
  public:
    /**
     * \brief Get the type ID.
     * \return the object TypeId
     */
    static TypeId GetTypeId();

    /**
     * \brief FifoQueueDisc constructor
     *
     * Creates a queue with enhanced simulation capabilities
     */
    FifoQueueDisc();

    ~FifoQueueDisc() override;

    /**
     * \brief Run complete network simulation
     * Creates complex topology, generates traffic, and collects statistics
     */
    void RunSimulation();

    /**
     * \brief Get packet loss statistics
     * \return packet loss ratio (0.0 to 1.0)
     */
    double GetPacketLossRatio() const;

    /**
     * \brief Get average delay
     * \return average packet delay in seconds
     */
    Time GetAverageDelay() const;

    /**
     * \brief Get throughput
     * \return throughput in bits per second
     */
    double GetThroughput() const;

    /**
     * \brief Print simulation results
     */
    void PrintResults() const;

    // Reasons for dropping packets
    static constexpr const char* LIMIT_EXCEEDED_DROP =
        "Queue disc limit exceeded"; //!< Packet dropped due to queue disc limit exceeded

    /**
     * \brief Trace source for packet enqueue events
     */
    TracedCallback<Ptr<const QueueDiscItem>> m_enqueueTrace;

    /**
     * \brief Trace source for packet dequeue events
     */
    TracedCallback<Ptr<const QueueDiscItem>> m_dequeueTrace;

    /**
     * \brief Trace source for packet drop events
     */
    TracedCallback<Ptr<const QueueDiscItem>> m_dropTrace;

  private:
    bool DoEnqueue(Ptr<QueueDiscItem> item) override;
    Ptr<QueueDiscItem> DoDequeue() override;
    Ptr<const QueueDiscItem> DoPeek() override;
    bool CheckConfig() override;
    void InitializeParams() override;

    // Simulation and statistics methods
    void CreateComplexTopology();
    void SetupTrafficGeneration();
    void InstallQueueDisc();
    void CalculateStatistics();

    // Statistics tracking
    void PacketEnqueueCallback(Ptr<const QueueDiscItem> item);
    void PacketDequeueCallback(Ptr<const QueueDiscItem> item);
    void PacketDropCallback(Ptr<const QueueDiscItem> item);

    // Member variables for statistics
    uint32_t m_totalPacketsSent;      //!< Total packets sent
    uint32_t m_totalPacketsReceived;  //!< Total packets received
    uint32_t m_totalPacketsDropped;   //!< Total packets dropped
    uint64_t m_totalBytesSent;        //!< Total bytes sent
    uint64_t m_totalBytesReceived;    //!< Total bytes received
    Time m_totalDelay;                //!< Total accumulated delay
    Time m_simulationStartTime;       //!< Simulation start time
    Time m_simulationEndTime;         //!< Simulation end time

    // Simulation parameters
    std::string m_linkBandwidth;      //!< Link bandwidth (e.g., "1Mbps")
    std::string m_linkDelay;          //!< Link delay (e.g., "10ms")
    uint32_t m_packetSize;            //!< Packet size in bytes
    std::string m_dataRate;           //!< Data generation rate
    Time m_simulationTime;            //!< Total simulation time
};

} // namespace ns3

#endif /* FIFO_QUEUE_DISC_H */
