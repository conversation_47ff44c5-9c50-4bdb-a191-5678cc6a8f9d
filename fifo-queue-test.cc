/*
 * FIFO Queue Simulation Test
 * Computer Networks and Communications 2 - Final Project
 * 
 * This file demonstrates the enhanced FIFO queue with simulation capabilities
 * Meeting all requirements:
 * - Complex network topology
 * - Packet loss and delay > 0
 * - Reduced bandwidth (not 100Mbps default)
 * - High packet rate and large packet size
 * - Sufficient simulation time for stable results
 */

#include "fifo-queue-disc.h"
#include "ns3/core-module.h"
#include "ns3/simulator.h"

using namespace ns3;

NS_LOG_COMPONENT_DEFINE("FifoQueueTest");

int main(int argc, char *argv[])
{
    // Enable logging
    LogComponentEnable("FifoQueueDisc", LOG_LEVEL_INFO);
    LogComponentEnable("FifoQueueTest", LOG_LEVEL_INFO);
    
    NS_LOG_INFO("Starting FIFO Queue Simulation Test");
    
    // Create FIFO queue disc instance
    Ptr<FifoQueueDisc> fifoQueue = CreateObject<FifoQueueDisc>();
    
    // Configure simulation parameters (can be changed via attributes)
    fifoQueue->SetAttribute("LinkBandwidth", StringValue("1Mbps"));    // Low bandwidth
    fifoQueue->SetAttribute("LinkDelay", StringValue("10ms"));         // Network delay
    fifoQueue->SetAttribute("PacketSize", UintegerValue(1024));        // Large packets
    fifoQueue->SetAttribute("DataRate", StringValue("2Mbps"));         // High data rate
    fifoQueue->SetAttribute("SimulationTime", TimeValue(Seconds(60))); // Long simulation
    fifoQueue->SetAttribute("MaxSize", QueueSizeValue(QueueSize("50p"))); // Small queue
    
    std::cout << "\n=== FIFO Queue Project - Computer Networks 2 ===" << std::endl;
    std::cout << "Student Project: FIFO Queue Simulation with ns-3" << std::endl;
    std::cout << "Requirements Implementation:" << std::endl;
    std::cout << "✓ Complex network topology with bottleneck" << std::endl;
    std::cout << "✓ Reduced bandwidth (1Mbps instead of 100Mbps)" << std::endl;
    std::cout << "✓ High packet rate (2Mbps) causing congestion" << std::endl;
    std::cout << "✓ Large packet size (1024 bytes)" << std::endl;
    std::cout << "✓ Long simulation time (60 seconds)" << std::endl;
    std::cout << "✓ Small queue size (50 packets) for packet loss" << std::endl;
    
    // Run the complete simulation
    fifoQueue->RunSimulation();
    
    // Display final results
    std::cout << "\n=== Final Performance Metrics ===" << std::endl;
    std::cout << "Packet Loss Ratio: " << fifoQueue->GetPacketLossRatio() * 100 << "%" << std::endl;
    std::cout << "Average Delay: " << fifoQueue->GetAverageDelay().GetMilliSeconds() << " ms" << std::endl;
    std::cout << "Throughput: " << fifoQueue->GetThroughput() / 1000000.0 << " Mbps" << std::endl;
    
    NS_LOG_INFO("FIFO Queue Simulation Test Completed");
    
    return 0;
}
